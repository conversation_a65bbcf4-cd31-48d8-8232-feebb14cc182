env: test
tier: backend

deployment:
  deploymentAnnotations:
  replicaCount: 1
  image:
    repository: harbor-edu.mos.ru/mes-teacherportfolio/s3integration
    tag: "latest"
    pullPolicy: IfNotPresent
  port: 8080
#   resources:
#     #limits:
#     #  cpu: 2
#     #  memory: 2048Mi
#     requests:
#       cpu: 1
#       memory: 2Gi
  restartPolicy: Always
  volume: |-
    - name: {{ .Chart.Name }}
      configMap:
        name: {{ .Chart.Name }}
        defaultMode: 0777
        items:
          - key: run.sh
            path: run.sh
  volumeMounts: |-
    - name: {{ .Chart.Name }}
      mountPath: /opt/run.sh
      subPath: run.sh
  livenessProbe:
   path: /actuator/health/
   port: 8080
   initDelay: 30
   failThreshold: 3
  readinessProbe:
   path: /actuator/health/
   port: 8080
   initDelay: 30
   failThreshold: 3
   readnessPeriod: 5

service:
  type: ClusterIP
  port: 80
  targetPort: 8080

ingress:
  enabled: true
  name: s3integration
  url: mes-kubernetes-test.mos.ru
  pathType: ImplementationSpecific
  path: /teacherportfolio/documents/v1
  servicePort: 80

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 2
  targetCPUUtilizationPercentage: 75
  targetMemoryUtilizationPercentage: 75

configmap:
  application:
    JAR_APP_PATH: /opt/s3Integration.jar
    SERVER_PATH:
    SERVER_PORT: 8080
    SERVER_ENTITY: DocumentStorage
    DATASOURCE_HOST: mesdb-cmndelta01t
    DATASOURCE_PORT: 5435
    DATASOURCE_DB: teacherportfolio
    DATASOURCE_USERNAME: portfolio
    DATASOURCE_PASSWORD: portfolio
    DATASOURCE_SCHEMA: public
    DATASOURCE_REPLICA_HOSTS: mesdb-cmndelta02t
    DATASOURCE_REPLICA_PORTS: 5435
    DATASOURCE_REPLICA_USERS: portfolio
    DATASOURCE_REPLICA_PASSWORDS: portfolio
    DATASOURCE_REPLICA_ENABLE: false
    S3_ENABLE: true
    S3_BUCKET_NAME: s3b-mes-teacherportfolio-test
    S3_HOST: https://s3-dc.mos.ru
    S3_ACCESS_KEY_ID: 4TsmJXejQXDNKSE4n8f6xh
    S3_ACCESS_KEY_SECRET: dmqJgubkBxQi7NPARpkRYHNgQfXxhuVNCJtLwPZrJPpn
    S3_PROXY: https://school-test.mos.ru/storage
    CEDS_HOST: http://*************/custom-api-2.0/rest/api/document/
    CEDS_LINK: https://school-test.mos.ru/teacherportfolio/app/documents/ext/getcontent
    CEDS_SYSTEM_CODE: ais_portfolio_uchit
    CEDS_PASSWORD: jdd356Fs56
    CEDS_REPOSITORY: DIT
    CEDS_DOC_CLASS: StudentAchievement
    AUPD_KEY_NAME: key-test.cer
